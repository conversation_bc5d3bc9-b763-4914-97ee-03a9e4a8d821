/**
 * 文件下载工具
 * 用于将数据导出为文件并下载
 */

/**
 * 下载文本文件
 * @param {string} content - 文件内容
 * @param {string} filename - 文件名
 * @param {string} mimeType - MIME类型
 */
export function downloadTextFile(content, filename, mimeType = 'text/plain') {
  const blob = new Blob([content], { type: mimeType })
  const url = URL.createObjectURL(blob)
  
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  link.style.display = 'none'
  
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  // 清理URL对象
  URL.revokeObjectURL(url)
}

/**
 * 下载GeoJSON文件
 * @param {string} geoJsonContent - GeoJSON内容
 * @param {string} filename - 文件名（不含扩展名）
 */
export function downloadGeoJSON(geoJsonContent, filename = 'export') {
  if (!geoJsonContent) {
    console.warn('没有可导出的GeoJSON数据')
    return
  }
  
  downloadTextFile(
    geoJsonContent,
    `${filename}.geojson`,
    'application/geo+json'
  )
}

/**
 * 下载KML文件
 * @param {string} kmlContent - KML内容
 * @param {string} filename - 文件名（不含扩展名）
 */
export function downloadKML(kmlContent, filename = 'export') {
  if (!kmlContent) {
    console.warn('没有可导出的KML数据')
    return
  }
  
  downloadTextFile(
    kmlContent,
    `${filename}.kml`,
    'application/vnd.google-earth.kml+xml'
  )
}

/**
 * 下载坐标列表文件
 * @param {string} coordinatesContent - 坐标列表内容
 * @param {string} filename - 文件名（不含扩展名）
 */
export function downloadCoordinates(coordinatesContent, filename = 'coordinates') {
  if (!coordinatesContent) {
    console.warn('没有可导出的坐标数据')
    return
  }
  
  downloadTextFile(
    coordinatesContent,
    `${filename}.json`,
    'application/json'
  )
}

/**
 * 获取当前时间戳字符串（用于文件名）
 * @returns {string} 格式化的时间戳
 */
export function getTimestamp() {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  
  return `${year}${month}${day}_${hours}${minutes}${seconds}`
}

/**
 * 生成带时间戳的文件名
 * @param {string} baseName - 基础文件名
 * @returns {string} 带时间戳的文件名
 */
export function generateFilename(baseName = 'export') {
  return `${baseName}_${getTimestamp()}`
}
