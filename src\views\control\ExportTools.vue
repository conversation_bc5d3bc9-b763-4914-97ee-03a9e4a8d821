<script setup>
import { ref, computed } from 'vue'
import globalLayerManager from '../../utils/GlobalLayerManager.js'
import {
  downloadGeoJSON,
  downloadKML,
  downloadCoordinates,
  generateFilename
} from '../../utils/FileDownloader.js'

const props = defineProps({
  map: Object,
  inNavbar: { type: Boolean, default: false }
})

// 导出选项
const exportOptions = [
  {
    key: 'draw_geojson',
    label: '绘制内容 (GeoJSON)',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
      <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"></path>
    </svg>`,
    action: () => exportDrawAsGeoJSON()
  },
  {
    key: 'draw_kml',
    label: '绘制内容 (KML)',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
      <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"></path>
    </svg>`,
    action: () => exportDrawAsKML()
  },
  {
    key: 'draw_coordinates',
    label: '绘制内容 (坐标)',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
      <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"></path>
    </svg>`,
    action: () => exportDrawAsCoordinates()
  },
  {
    key: 'measure_geojson',
    label: '测量数据 (GeoJSON)',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
      <path d="M21 6H3c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm0 10H3V8h2v4h2V8h2v4h2V8h2v4h2V8h2v4h2V8h2v8z"></path>
    </svg>`,
    action: () => exportMeasureAsGeoJSON()
  },
  {
    key: 'measure_kml',
    label: '测量数据 (KML)',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
      <path d="M21 6H3c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm0 10H3V8h2v4h2V8h2v4h2V8h2v4h2V8h2v4h2V8h2v8z"></path>
    </svg>`,
    action: () => exportMeasureAsKML()
  },
  {
    key: 'measure_coordinates',
    label: '测量数据 (坐标)',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
      <path d="M21 6H3c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm0 10H3V8h2v4h2V8h2v4h2V8h2v4h2V8h2v4h2V8h2v8z"></path>
    </svg>`,
    action: () => exportMeasureAsCoordinates()
  },
  {
    key: 'all_geojson',
    label: '全部数据 (GeoJSON)',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
      <path d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V19H5V5H19Z"></path>
    </svg>`,
    action: () => exportAllAsGeoJSON()
  },
  {
    key: 'all_kml',
    label: '全部数据 (KML)',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
      <path d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V19H5V5H19Z"></path>
    </svg>`,
    action: () => exportAllAsKML()
  }
]

// 计算可用的导出选项
const availableOptions = computed(() => {
  const drawCount = globalLayerManager.getDrawFeatureCount()
  const measureCount = globalLayerManager.getMeasureFeatureCount()

  return exportOptions.filter(option => {
    if (option.key.startsWith('draw_')) {
      return drawCount > 0
    } else if (option.key.startsWith('measure_')) {
      return measureCount > 0
    } else if (option.key.startsWith('all_')) {
      return drawCount > 0 || measureCount > 0
    }
    return true
  })
})

// 导出函数
const exportDrawAsGeoJSON = () => {
  const content = globalLayerManager.exportDrawAsGeoJSON()
  if (content) {
    downloadGeoJSON(content, generateFilename('绘制内容'))
    console.log('绘制内容已导出为GeoJSON')
  } else {
    console.warn('没有可导出的绘制内容')
  }
}

const exportDrawAsKML = () => {
  const content = globalLayerManager.exportDrawAsKML()
  if (content) {
    downloadKML(content, generateFilename('绘制内容'))
    console.log('绘制内容已导出为KML')
  } else {
    console.warn('没有可导出的绘制内容')
  }
}

const exportDrawAsCoordinates = () => {
  const content = globalLayerManager.exportDrawAsCoordinates()
  if (content) {
    downloadCoordinates(content, generateFilename('绘制坐标'))
    console.log('绘制坐标已导出')
  } else {
    console.warn('没有可导出的绘制内容')
  }
}

const exportMeasureAsGeoJSON = () => {
  const content = globalLayerManager.exportMeasureAsGeoJSON()
  if (content) {
    downloadGeoJSON(content, generateFilename('测量数据'))
    console.log('测量数据已导出为GeoJSON')
  } else {
    console.warn('没有可导出的测量数据')
  }
}

const exportMeasureAsKML = () => {
  const content = globalLayerManager.exportMeasureAsKML()
  if (content) {
    downloadKML(content, generateFilename('测量数据'))
    console.log('测量数据已导出为KML')
  } else {
    console.warn('没有可导出的测量数据')
  }
}

const exportMeasureAsCoordinates = () => {
  const content = globalLayerManager.exportMeasureAsCoordinates()
  if (content) {
    downloadCoordinates(content, generateFilename('测量坐标'))
    console.log('测量坐标已导出')
  } else {
    console.warn('没有可导出的测量数据')
  }
}

const exportAllAsGeoJSON = () => {
  const content = globalLayerManager.exportAllAsGeoJSON()
  if (content) {
    downloadGeoJSON(content, generateFilename('全部数据'))
    console.log('全部数据已导出为GeoJSON')
  } else {
    console.warn('没有可导出的数据')
  }
}

const exportAllAsKML = () => {
  const content = globalLayerManager.exportAllAsKML()
  if (content) {
    downloadKML(content, generateFilename('全部数据'))
    console.log('全部数据已导出为KML')
  } else {
    console.warn('没有可导出的数据')
  }
}
</script>

<template>
  <div class="export-tools-container" :class="{ 'export-tools-navbar': props.inNavbar }">
    <div class="export-tools-content">
      <div class="export-tools-header">
        <span class="export-tools-title">数据导出</span>
      </div>

      <div v-if="availableOptions.length === 0" class="no-data-message">
        暂无可导出的数据
      </div>

      <div v-else class="export-options">
        <div
          v-for="option in availableOptions"
          :key="option.key"
          class="export-option"
          @click="option.action"
        >
          <span class="option-icon" v-html="option.icon"></span>
          <span class="option-label">{{ option.label }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.export-tools-container {
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.10);
  padding: 0;
  min-width: 200px;
  max-width: 280px;
}

.export-tools-navbar {
  border-radius: 10px;
}

.export-tools-content {
  padding: 16px;
}

.export-tools-header {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.export-tools-title {
  font-weight: 600;
  color: #1890ff;
  font-size: 16px;
}

.no-data-message {
  color: #999;
  text-align: center;
  padding: 20px 0;
  font-size: 14px;
}

.export-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.export-option {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
  border: 1px solid transparent;
}

.export-option:hover {
  background-color: #f5f5f5;
  border-color: #d9d9d9;
}

.option-icon {
  margin-right: 10px;
  color: #1890ff;
  display: flex;
  align-items: center;
}

.option-label {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
}
</style>
