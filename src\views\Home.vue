<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import Map from './Map.vue'
import DrawTools from './control/DrawTools.vue'
import MeasureTools from './control/MeasureTools.vue'
import ExportTools from './control/ExportTools.vue'
import MapToolBar from '../components/MapToolBar.vue'
import ProvinceSearch from './control/ProvinceSearch.vue'
import HeritageSearch from './control/HeritageSearch.vue'
import TopLeftProvinceButton from '../components/TopLeftProvinceButton.vue'
import TopLeftExportButton from '../components/TopLeftExportButton.vue'

// 功能状态
const positionEnabled = ref(false)
const layerManagerEnabled = ref(false)
const mapOperationsEnabled = ref(false)
const drawToolsEnabled = ref(false)
const measureToolsEnabled = ref(false)
const exportToolsEnabled = ref(false)

// 当前选中的底图
const currentBase = ref('tian')
// 底图类型
const baseTypes = [
  {
    key: 'tian',
    label: '天地图',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="22" height="22" fill="#1890ff">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"></path>
    </svg>`
  },
  {
    key: 'baidu',
    label: '百度地图',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="22" height="22" fill="#3470ff">
      <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"></path>
    </svg>`
  },
  {
    key: 'gaode',
    label: '高德地图',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="22" height="22" fill="#fa8c16">
      <path d="M20.5 3l-.16.03L15 5.1 9 3 3.36 4.9c-.21.07-.36.25-.36.48V20.5c0 .28.22.5.5.5l.16-.03L9 18.9l6 2.1 5.64-1.9c.21-.07.36-.25.36-.48V3.5c0-.28-.22-.5-.5-.5zM15 19l-6-2.11V5l6 2.11V19z"></path>
    </svg>`
  }
]
// 地图组件引用
const mapRef = ref(null)
// 地图图层引用
const mapLayers = ref({})

// 下拉菜单
const dropdown = ref({ key: '', rect: null })
// 省份搜索
const provinceSearchVisible = ref(false)
const provinceSearchRect = ref(null)

// 世界遗产搜索
const heritageSearchVisible = ref(false)
const heritageSearchRect = ref(null)

// 导出工具状态
const exportToolsVisible = ref(false)
const exportToolsRect = ref(null)

// 全屏模式
const fullScreenMode = ref(false)

const router = useRouter()

// 监听地图组件的加载
onMounted(() => {
  const checkMapComponent = () => {
    if (mapRef.value && mapRef.value.mousePositionRef) {
      positionEnabled.value = mapRef.value.mousePositionRef.showCoordinatesEnabled
      initLayers()
    } else {
      setTimeout(checkMapComponent, 500)
    }
  }
  checkMapComponent()

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeyDown)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})

// 初始化图层引用
const initLayers = () => {
  if (!mapRef.value || !mapRef.value.mapInstance) return
  const layers = mapRef.value.mapInstance.getLayers().getArray()
  mapLayers.value = {}
  layers.forEach(layer => {
    const name = layer.get('name')
    if (name && ['tian', 'tianlabel', 'baidu', 'gaode'].includes(name)) {
      mapLayers.value[name] = layer
      if (layer.getVisible() && name !== 'tianlabel') {
        currentBase.value = name
      }
    }
  })
}

function handleToolbarToggle(key, rect) {
  // 处理全屏按钮
  if (key === 'fullscreen') {
    toggleFullScreenMode()
    return
  }

  // 关闭
  if (dropdown.value.key === key) {
    dropdown.value = { key: '', rect: null }
    layerManagerEnabled.value = false
    mapOperationsEnabled.value = false
    drawToolsEnabled.value = false
    measureToolsEnabled.value = false
    if (key === 'locate') positionEnabled.value = false
    return
  }
  dropdown.value = { key, rect: rect || null }
  layerManagerEnabled.value = key === 'layer'
  mapOperationsEnabled.value = key === 'map'
  drawToolsEnabled.value = key === 'draw'
  measureToolsEnabled.value = key === 'measure'
  if (key === 'locate') {
    // 切换定位功能
    if (mapRef.value && mapRef.value.togglePositionTool) {
      positionEnabled.value = mapRef.value.togglePositionTool()
    } else {
      positionEnabled.value = !positionEnabled.value
    }
    // 关闭其它弹窗
    layerManagerEnabled.value = false
    mapOperationsEnabled.value = false
    drawToolsEnabled.value = false
    measureToolsEnabled.value = false
    exportToolsEnabled.value = false
    dropdown.value = { key: '', rect: null }
  }
}

const dropdownMenuStyle = computed(() => {
  if (!dropdown.value.rect) return {}
  return {
    position: 'fixed',
    top: dropdown.value.rect.bottom + 6 + 'px',
    left: dropdown.value.rect.left + 'px',
    zIndex: 10020,
    minWidth: dropdown.value.rect.width + 'px'
  }
})

// 省份搜索下拉面板的样式
const provinceSearchDropdownStyle = computed(() => {
  if (!provinceSearchRect.value) return {}
  const buttonCenterX = provinceSearchRect.value.left + (provinceSearchRect.value.width / 2);
  const panelWidth = 260;
  const leftPosition = buttonCenterX - (panelWidth / 2);
  const safeLeftPosition = Math.max(10, leftPosition);
  const rightEdge = safeLeftPosition + panelWidth;
  const finalLeftPosition = rightEdge > window.innerWidth - 10
    ? window.innerWidth - panelWidth - 10
    : safeLeftPosition;
  const topPosition = provinceSearchRect.value.bottom + 10;
  const adjustedLeftPosition = Math.max(finalLeftPosition, 70);

  return {
    position: 'fixed',
    top: topPosition + 'px',
    left: adjustedLeftPosition + 'px',
    zIndex: 10020,
    width: panelWidth + 'px',
    maxHeight: '80vh',
    overflowY: 'auto'
  }
})

// 世界遗产搜索下拉面板的样式
const heritageSearchDropdownStyle = computed(() => {
  if (!heritageSearchRect.value) return {}
  const buttonCenterX = heritageSearchRect.value.left + (heritageSearchRect.value.width / 2);
  const panelWidth = 260;
  const leftPosition = buttonCenterX - (panelWidth / 2);
  const safeLeftPosition = Math.max(10, leftPosition);
  const rightEdge = safeLeftPosition + panelWidth;
  const finalLeftPosition = rightEdge > window.innerWidth - 10
    ? window.innerWidth - panelWidth - 10
    : safeLeftPosition;
  const topPosition = heritageSearchRect.value.bottom + 10;
  const adjustedLeftPosition = Math.max(finalLeftPosition, 70);

  return {
    position: 'fixed',
    top: topPosition + 'px',
    left: adjustedLeftPosition + 'px',
    zIndex: 10020,
    width: panelWidth + 'px',
    maxHeight: '80vh',
    overflowY: 'auto'
  }
})

// 导出工具下拉面板的样式
const exportToolsDropdownStyle = computed(() => {
  if (!exportToolsRect.value) return {}
  const buttonCenterX = exportToolsRect.value.left + (exportToolsRect.value.width / 2);
  const panelWidth = 280;
  const leftPosition = buttonCenterX - (panelWidth / 2);
  const safeLeftPosition = Math.max(10, leftPosition);
  const rightEdge = safeLeftPosition + panelWidth;
  const finalLeftPosition = rightEdge > window.innerWidth - 10
    ? window.innerWidth - panelWidth - 10
    : safeLeftPosition;
  const topPosition = exportToolsRect.value.bottom + 10;
  const adjustedLeftPosition = Math.max(finalLeftPosition, 70);

  return {
    position: 'fixed',
    top: topPosition + 'px',
    left: adjustedLeftPosition + 'px',
    zIndex: 10020,
    width: panelWidth + 'px',
    maxHeight: '80vh',
    overflowY: 'auto'
  }
})

// 设置图层可见性
const setBaseVisible = (type) => {
  Object.entries(mapLayers.value).forEach(([key, layer]) => {
    if (key === 'tianlabel') {
      layer.setVisible(type === 'tian')
    } else {
      layer.setVisible(key === type)
    }
  })
}

// 切换底图
const switchBase = (type) => {
  currentBase.value = type
  setBaseVisible(type)
  if (mapRef.value && mapRef.value.mapInstance) {
    mapRef.value.mapInstance.getLayers().forEach(layer => {
      if (layer.get('name') === type) {
        const event = {
          type: 'propertychange',
          key: 'visible',
          target: layer
        }
        layer.dispatchEvent(event)
      }
    })
  }
}

// 处理地图创建完成事件
const handleMapCreated = (map) => {
  console.log('地图创建完成')
}

// 切换全屏模式
const toggleFullScreenMode = () => {
  fullScreenMode.value = !fullScreenMode.value
  // 如果进入全屏模式，关闭所有下拉菜单
  if (fullScreenMode.value) {
    dropdown.value = { key: '', rect: null }
    provinceSearchVisible.value = false
    heritageSearchVisible.value = false
  }
}

// 监听ESC键退出全屏模式
const handleKeyDown = (event) => {
  if (event.key === 'Escape' && fullScreenMode.value) {
    fullScreenMode.value = false
  }
}



// 处理省份搜索按钮点击事件
const handleTopLeftProvinceSearchToggle = ({ rect }) => {
  provinceSearchVisible.value = !provinceSearchVisible.value
  if (provinceSearchVisible.value && rect) {
    provinceSearchRect.value = rect
  } else {
    provinceSearchRect.value = null
  }
  if (provinceSearchVisible.value) {
    dropdown.value = { key: '', rect: null }
    layerManagerEnabled.value = false
    mapOperationsEnabled.value = false
    drawToolsEnabled.value = false
    measureToolsEnabled.value = false
    exportToolsEnabled.value = false
    heritageSearchVisible.value = false
    heritageSearchRect.value = null
    exportToolsVisible.value = false
    exportToolsRect.value = null
  }
}

// 处理世界遗产搜索按钮点击事件
const handleHeritageSearchToggle = ({ rect }) => {
  heritageSearchVisible.value = !heritageSearchVisible.value
  if (heritageSearchVisible.value && rect) {
    heritageSearchRect.value = rect
  } else {
    heritageSearchRect.value = null
  }
  if (heritageSearchVisible.value) {
    dropdown.value = { key: '', rect: null }
    layerManagerEnabled.value = false
    mapOperationsEnabled.value = false
    drawToolsEnabled.value = false
    measureToolsEnabled.value = false
    exportToolsEnabled.value = false
    provinceSearchVisible.value = false
    provinceSearchRect.value = null
    exportToolsVisible.value = false
    exportToolsRect.value = null
  }
}

// 处理导出工具按钮点击事件
const handleTopLeftExportToolsToggle = ({ rect }) => {
  exportToolsVisible.value = !exportToolsVisible.value
  if (exportToolsVisible.value && rect) {
    exportToolsRect.value = rect
  } else {
    exportToolsRect.value = null
  }
  if (exportToolsVisible.value) {
    dropdown.value = { key: '', rect: null }
    layerManagerEnabled.value = false
    mapOperationsEnabled.value = false
    drawToolsEnabled.value = false
    measureToolsEnabled.value = false
    exportToolsEnabled.value = false
    provinceSearchVisible.value = false
    provinceSearchRect.value = null
    heritageSearchVisible.value = false
    heritageSearchRect.value = null
  }
}

// 处理位置选择事件
const handleLocationSelected = (location) => {
  provinceSearchVisible.value = false
  provinceSearchRect.value = null
  heritageSearchVisible.value = false
  heritageSearchRect.value = null
  exportToolsVisible.value = false
  exportToolsRect.value = null

  console.log('选择的位置:', location)
}

</script>

<template>
  <MapToolBar
    v-if="!fullScreenMode"
    :active="{layer: layerManagerEnabled, map: mapOperationsEnabled, draw: drawToolsEnabled, measure: measureToolsEnabled, locate: positionEnabled, fullscreen: fullScreenMode}"
    @toggle="({key, rect}) => handleToolbarToggle(key, rect)"
    @toggle-heritage-search="handleHeritageSearchToggle"
  />

  <!-- 全屏模式下的退出按钮 -->
  <div v-if="fullScreenMode" class="fullscreen-exit-btn" @click="toggleFullScreenMode" title="退出全屏 (ESC)">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="currentColor">
      <path d="M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z"></path>
    </svg>
  </div>

  <!-- 图层管理下拉菜单 -->
  <div
    class="layer-dropdown"
    v-if="dropdown.key === 'layer' && dropdown.rect && !fullScreenMode"
    :style="dropdownMenuStyle"
  >
    <div class="dropdown-header">
      <span class="dropdown-title">底图</span>
    </div>
    <div
      v-for="item in baseTypes"
      :key="item.key"
      class="layer-dropdown-item"
      :class="{ active: currentBase === item.key }"
      @click="switchBase(item.key)"
    >
      <span class="dropdown-icon" v-html="item.icon"></span>
      <span class="dropdown-label">{{ item.label }}</span>
      <span class="dropdown-check" v-if="currentBase === item.key">✔</span>
    </div>
  </div>

  <!-- 地图操作下拉菜单 -->
  <div
    class="map-ops-dropdown"
    v-if="dropdown.key === 'map' && dropdown.rect && !fullScreenMode"
    :style="dropdownMenuStyle"
  >
    <div class="dropdown-header">
      <span class="dropdown-title">操作</span>
    </div>
    <div class="dropdown-item" @click="mapRef && mapRef.zoomIn()">
      <span class="dropdown-icon">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
          <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"></path>
        </svg>
      </span>
      <span class="dropdown-label">放大</span>
    </div>
    <div class="dropdown-item" @click="mapRef && mapRef.zoomOut()">
      <span class="dropdown-icon">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
          <path d="M19 13H5v-2h14v2z"></path>
        </svg>
      </span>
      <span class="dropdown-label">缩小</span>
    </div>
    <div class="dropdown-divider"></div>
    <div class="dropdown-item" @click="mapRef && mapRef.moveToWuhan()">
      <span class="dropdown-icon">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
          <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"></path>
        </svg>
      </span>
      <span class="dropdown-label">武汉</span>
    </div>
    <div class="dropdown-item" @click="mapRef && mapRef.resetMap()">
      <span class="dropdown-icon">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
          <path d="M17.65 6.35A7.958 7.958 0 0 0 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08A5.99 5.99 0 0 1 12 18c-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"></path>
        </svg>
      </span>
      <span class="dropdown-label">复位</span>
    </div>
  </div>

  <!-- 绘制工具弹窗 -->
  <div
    v-if="dropdown.key === 'draw' && dropdown.rect && mapRef && mapRef.mapInstance && !fullScreenMode"
    :style="dropdownMenuStyle"
    style="background:#fff;border-radius:10px;box-shadow:0 4px 16px rgba(0,0,0,0.10);padding:0;z-index:10020;"
  >
    <DrawTools :map="mapRef.mapInstance" :inNavbar="true" class="draw-tools-navbar" />
  </div>
  <!-- 测量工具弹窗 -->
  <div
    v-if="dropdown.key === 'measure' && dropdown.rect && mapRef && mapRef.mapInstance && !fullScreenMode"
    :style="dropdownMenuStyle"
    style="background:#fff;border-radius:10px;box-shadow:0 4px 16px rgba(0,0,0,0.10);padding:0;z-index:10020;"
  >
    <MeasureTools :map="mapRef.mapInstance" :inNavbar="true" />
  </div>

  <!-- 省份搜索下拉面板 -->
  <div
    v-if="provinceSearchVisible && provinceSearchRect && mapRef && mapRef.mapInstance && !fullScreenMode"
    class="province-search-dropdown"
    :style="provinceSearchDropdownStyle"
  >
    <ProvinceSearch :map="mapRef.mapInstance" @location-selected="handleLocationSelected" />
  </div>
    <!-- 左上角省份搜索按钮 -->
  <TopLeftProvinceButton v-if="!fullScreenMode" @toggle-province-search="handleTopLeftProvinceSearchToggle" />

  <!-- 左上角导出按钮 -->
  <TopLeftExportButton v-if="!fullScreenMode" @toggle-export-tools="handleTopLeftExportToolsToggle" />

  <!-- 导出工具下拉面板 -->
  <div
    v-if="exportToolsVisible && exportToolsRect && mapRef && mapRef.mapInstance && !fullScreenMode"
    class="export-tools-dropdown"
    :style="exportToolsDropdownStyle"
  >
    <ExportTools :map="mapRef.mapInstance" :inNavbar="false" />
  </div>

  <!-- 世界遗产搜索下拉面板 -->
  <div
    v-if="heritageSearchVisible && heritageSearchRect && mapRef && mapRef.mapInstance && !fullScreenMode"
    class="heritage-search-dropdown"
    :style="heritageSearchDropdownStyle"
  >
    <HeritageSearch :map="mapRef.mapInstance" @location-selected="handleLocationSelected" />
  </div>

  <!-- 内容区域 -->
  <div class="content">
    <div class="map-container">
      <!-- 地图组件 -->
      <Map ref="mapRef" @created="handleMapCreated" />
    </div>
  </div>
</template>

<style>
.layer-dropdown,
.map-ops-dropdown {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.10);
  min-width: 160px;
  padding: 0;
  overflow: hidden;
  border: none;
}
.dropdown-header {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}
.dropdown-title {
  font-size: 15px;
  font-weight: 600;
  color: #222;
}
.layer-dropdown-item,
.dropdown-item {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #222;
  background: #fff;
  font-size: 15px;
}
.layer-dropdown-item:hover,
.dropdown-item:hover {
  background-color: #f5f7fa;
  color: #1890ff;
}
.layer-dropdown-item.active {
  background-color: #e6f7ff;
  color: #1890ff;
  font-weight: 600;
}
.dropdown-label {
  flex-grow: 1;
}
.dropdown-check {
  margin-left: 8px;
  font-weight: bold;
  color: #1890ff;
}
.dropdown-icon {
  margin-right: 12px;
  font-size: 18px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.dropdown-divider {
  height: 1px;
  background-color: #f0f0f0;
  margin: 8px 0;
}
.content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}
.map-container {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 省份搜索下拉面板样式 */
.province-search-dropdown, .heritage-search-dropdown, .export-tools-dropdown {
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.12);
  padding: 0;
  overflow: visible;
  border: 1px solid #e0e0e0;
  max-width: 280px;
  position: relative;
  z-index: 10000;
}

/* 添加一个小三角形指向按钮 */
.province-search-dropdown::before, .heritage-search-dropdown::before, .export-tools-dropdown::before {
  content: '';
  position: absolute;
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #fff;
  filter: drop-shadow(0 -1px 1px rgba(0,0,0,0.1));
  z-index: 1;
}

/* 全屏退出按钮样式 */
.fullscreen-exit-btn {
  position: fixed;
  top: 24px;
  right: 24px;
  z-index: 10030;
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.fullscreen-exit-btn:hover {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  transform: scale(1.05);
}

.fullscreen-exit-btn svg {
  color: #666;
  transition: color 0.3s ease;
}

.fullscreen-exit-btn:hover svg {
  color: #409EFF;
}


</style>