/**
 * 全局图层管理器
 * 用于管理所有绘制和测量的图形，确保在功能切换时不会丢失
 */

import { Vector as VectorLayer } from 'ol/layer'
import { Vector as VectorSource } from 'ol/source'
import { Style, Fill, Stroke, Circle as CircleStyle } from 'ol/style'
import { GeoJSON, KML } from 'ol/format'
import { transform } from 'ol/proj'
import { getLength, getArea } from 'ol/sphere'

class GlobalLayerManager {
  constructor() {
    this.map = null
    this.layers = {
      draw: null,      // 绘制图层
      measure: null    // 测量图层
    }
    this.sources = {
      draw: null,      // 绘制数据源
      measure: null    // 测量数据源
    }
    this.overlays = {
      draw: [],        // 绘制相关的覆盖物
      measure: []      // 测量相关的覆盖物
    }
    this.initialized = false
  }

  /**
   * 初始化图层管理器
   * @param {Object} map - OpenLayers地图实例
   */
  init(map) {
    if (!map || this.initialized) return

    this.map = map
    this.createLayers()
    this.initialized = true

    console.log('全局图层管理器初始化完成')
  }

  /**
   * 创建持久化图层
   */
  createLayers() {
    // 创建绘制图层
    this.sources.draw = new VectorSource()
    this.layers.draw = new VectorLayer({
      source: this.sources.draw,
      style: this.getDrawStyle(),
      zIndex: 1000
    })
    this.layers.draw.set('name', 'global-draw-layer')

    // 创建测量图层
    this.sources.measure = new VectorSource()
    this.layers.measure = new VectorLayer({
      source: this.sources.measure,
      style: this.getMeasureStyle(),
      zIndex: 1001
    })
    this.layers.measure.set('name', 'global-measure-layer')

    // 添加到地图
    this.map.addLayer(this.layers.draw)
    this.map.addLayer(this.layers.measure)
  }

  /**
   * 获取绘制图层的样式
   */
  getDrawStyle() {
    return (feature) => {
      const geometry = feature.getGeometry()
      const geometryType = geometry.getType()
      const featureName = feature.get('name') || ''

      // 基础样式
      const styles = []

      if (geometryType === 'Point') {
        // 点样式
        styles.push(new Style({
          image: new CircleStyle({
            radius: 7,
            fill: new Fill({
              color: 'rgba(255, 0, 0, 0.7)'
            }),
            stroke: new Stroke({
              color: 'white',
              width: 2
            })
          })
        }))
      } else if (geometryType === 'LineString') {
        // 线样式
        styles.push(new Style({
          stroke: new Stroke({
            color: '#1890ff',
            width: 3
          })
        }))
      } else if (geometryType === 'Polygon') {
        // 多边形样式
        styles.push(new Style({
          fill: new Fill({
            color: 'rgba(24, 144, 255, 0.2)'
          }),
          stroke: new Stroke({
            color: '#1890ff',
            width: 2
          })
        }))
      }

      return styles
    }
  }

  /**
   * 获取测量图层的样式
   */
  getMeasureStyle() {
    return (feature) => {
      const geometry = feature.getGeometry()
      const geometryType = geometry.getType()

      // 基础样式
      const styles = []

      if (geometryType === 'Point') {
        // 点样式
        styles.push(new Style({
          image: new CircleStyle({
            radius: 5,
            stroke: new Stroke({
              color: '#ffcc02',
              width: 2
            }),
            fill: new Fill({
              color: 'rgba(255, 255, 255, 0.8)'
            })
          })
        }))
      } else if (geometryType === 'LineString') {
        // 线样式 - 测量距离
        styles.push(new Style({
          stroke: new Stroke({
            color: '#ffcc02',
            width: 2,
            lineDash: [10, 10]
          })
        }))
      } else if (geometryType === 'Polygon') {
        // 多边形样式 - 测量面积
        styles.push(new Style({
          fill: new Fill({
            color: 'rgba(255, 204, 2, 0.2)'
          }),
          stroke: new Stroke({
            color: '#ffcc02',
            width: 2,
            lineDash: [10, 10]
          })
        }))
      }

      return styles
    }
  }

  /**
   * 获取绘制数据源
   */
  getDrawSource() {
    return this.sources.draw
  }

  /**
   * 获取测量数据源
   */
  getMeasureSource() {
    return this.sources.measure
  }

  /**
   * 获取绘制图层
   */
  getDrawLayer() {
    return this.layers.draw
  }

  /**
   * 获取测量图层
   */
  getMeasureLayer() {
    return this.layers.measure
  }

  /**
   * 添加覆盖物到指定类型
   * @param {string} type - 类型 ('draw' 或 'measure')
   * @param {Object} overlay - 覆盖物对象
   */
  addOverlay(type, overlay) {
    if (!this.overlays[type]) return

    this.overlays[type].push(overlay)
    if (this.map) {
      this.map.addOverlay(overlay)
    }
  }

  /**
   * 移除指定类型的所有覆盖物
   * @param {string} type - 类型 ('draw' 或 'measure')
   */
  removeOverlays(type) {
    if (!this.overlays[type] || !this.map) return

    this.overlays[type].forEach(overlay => {
      this.map.removeOverlay(overlay)
      const element = overlay.getElement()
      if (element && element.parentNode) {
        element.parentNode.removeChild(element)
      }
    })
    this.overlays[type] = []
  }

  /**
   * 清除指定类型的绘制内容
   * @param {string} featureType - 要清除的要素类型 ('Point', 'LineString', 'Polygon', 'bezier')
   */
  clearDrawByType(featureType) {
    if (!this.sources.draw) return

    const features = this.sources.draw.getFeatures()
    const featuresToRemove = []
    const overlaysToRemove = []

    features.forEach(feature => {
      const geometry = feature.getGeometry()
      const geometryType = geometry.getType()
      const isBezier = feature.get('bezier')
      const featureName = feature.get('name') || ''

      let shouldRemove = false

      if (featureType === 'Point' && geometryType === 'Point') {
        shouldRemove = true
      } else if (featureType === 'LineString' && geometryType === 'LineString' && !isBezier) {
        shouldRemove = true
      } else if (featureType === 'bezier' && (isBezier || featureName.includes('曲线'))) {
        shouldRemove = true
      } else if (featureType === 'Polygon' && geometryType === 'Polygon') {
        shouldRemove = true
      }

      if (shouldRemove) {
        featuresToRemove.push(feature)
      }
    })

    // 移除要素
    featuresToRemove.forEach(feature => {
      this.sources.draw.removeFeature(feature)
    })

    // 移除相关的绘制覆盖物
    this.overlays.draw = this.overlays.draw.filter(overlay => {
      const element = overlay.getElement()
      if (element) {
        const content = element.innerHTML || ''
        let shouldRemoveOverlay = false

        if (featureType === 'Point' && content.includes('点 ')) {
          shouldRemoveOverlay = true
        } else if (featureType === 'LineString' && content.includes('线 ')) {
          shouldRemoveOverlay = true
        } else if (featureType === 'bezier' && content.includes('曲线')) {
          shouldRemoveOverlay = true
        } else if (featureType === 'Polygon' && content.includes('多边形')) {
          shouldRemoveOverlay = true
        }

        if (shouldRemoveOverlay) {
          this.map.removeOverlay(overlay)
          if (element.parentNode) {
            element.parentNode.removeChild(element)
          }
          return false
        }
      }
      return true
    })

    // 移除相关的测量覆盖物
    this.overlays.measure = this.overlays.measure.filter(overlay => {
      const element = overlay.getElement()
      if (element) {
        const content = element.innerHTML || ''
        let shouldRemoveOverlay = false

        if (featureType === 'LineString') {
          // 清除线相关的距离标签（包含距离单位的标签和线段标记）
          if (content.includes(' m') || content.includes(' km') ||
              content.includes('距离') || content.includes('长度') ||
              element.className.includes('ol-tooltip-segment') ||
              element.className.includes('ol-tooltip-static')) {
            shouldRemoveOverlay = true
          }
        } else if (featureType === 'Polygon') {
          // 清除多边形相关的面积标签和顶点标记
          if (content.includes('总面积:') || content.includes('面积') ||
              content.includes(' m²') || content.includes(' km²') ||
              element.className.includes('ol-tooltip-vertex') ||
              element.className.includes('ol-tooltip-result') ||
              element.className.includes('ol-tooltip-static')) {
            shouldRemoveOverlay = true
          }
        }

        if (shouldRemoveOverlay) {
          this.map.removeOverlay(overlay)
          if (element.parentNode) {
            element.parentNode.removeChild(element)
          }
          return false
        }
      }
      return true
    })

    console.log(`已清除所有${featureType}类型的绘制内容`)
  }

  /**
   * 清除绘制内容
   */
  clearDraw() {
    if (this.sources.draw) {
      this.sources.draw.clear()
    }
    this.removeOverlays('draw')
    console.log('已清除所有绘制内容')
  }

  /**
   * 清除测量内容
   */
  clearMeasure() {
    if (this.sources.measure) {
      this.sources.measure.clear()
    }
    this.removeOverlays('measure')
    console.log('已清除所有测量内容')
  }

  /**
   * 清除所有内容
   */
  clearAll() {
    this.clearDraw()
    this.clearMeasure()
    console.log('已清除所有绘制和测量内容')
  }

  /**
   * 获取绘制的要素数量
   */
  getDrawFeatureCount() {
    return this.sources.draw ? this.sources.draw.getFeatures().length : 0
  }

  /**
   * 获取测量的要素数量
   */
  getMeasureFeatureCount() {
    return this.sources.measure ? this.sources.measure.getFeatures().length : 0
  }

  /**
   * 获取所有绘制的要素
   */
  getDrawFeatures() {
    return this.sources.draw ? this.sources.draw.getFeatures() : []
  }

  /**
   * 获取所有测量的要素
   */
  getMeasureFeatures() {
    return this.sources.measure ? this.sources.measure.getFeatures() : []
  }

  /**
   * 导出绘制数据为GeoJSON
   */
  exportDrawAsGeoJSON() {
    if (!this.sources.draw) return null

    const format = new GeoJSON()
    const features = this.sources.draw.getFeatures()
    return format.writeFeatures(features, {
      featureProjection: 'EPSG:3857',
      dataProjection: 'EPSG:4326'
    })
  }

  /**
   * 导出测量数据为GeoJSON
   */
  exportMeasureAsGeoJSON() {
    if (!this.sources.measure) return null

    const format = new GeoJSON()
    const features = this.sources.measure.getFeatures()
    return format.writeFeatures(features, {
      featureProjection: 'EPSG:3857',
      dataProjection: 'EPSG:4326'
    })
  }

  /**
   * 导出绘制数据为KML
   */
  exportDrawAsKML() {
    if (!this.sources.draw) return null

    const format = new KML()
    const features = this.sources.draw.getFeatures()
    return format.writeFeatures(features, {
      featureProjection: 'EPSG:3857',
      dataProjection: 'EPSG:4326'
    })
  }

  /**
   * 导出测量数据为KML
   */
  exportMeasureAsKML() {
    if (!this.sources.measure) return null

    const format = new KML()
    const features = this.sources.measure.getFeatures()
    return format.writeFeatures(features, {
      featureProjection: 'EPSG:3857',
      dataProjection: 'EPSG:4326'
    })
  }

  /**
   * 导出绘制数据为坐标列表
   */
  exportDrawAsCoordinates() {
    if (!this.sources.draw) return null

    const features = this.sources.draw.getFeatures()
    const data = []

    features.forEach((feature, index) => {
      const geometry = feature.getGeometry()
      const geometryType = geometry.getType()
      const name = feature.get('name') || `${geometryType} ${index + 1}`

      let coordinates = []

      if (geometryType === 'Point') {
        const coord = transform(geometry.getCoordinates(), 'EPSG:3857', 'EPSG:4326')
        coordinates = [{ lng: coord[0], lat: coord[1] }]
      } else if (geometryType === 'LineString') {
        coordinates = geometry.getCoordinates().map(coord => {
          const transformed = transform(coord, 'EPSG:3857', 'EPSG:4326')
          return { lng: transformed[0], lat: transformed[1] }
        })
      } else if (geometryType === 'Polygon') {
        // 只导出外环坐标
        const ring = geometry.getCoordinates()[0]
        coordinates = ring.map(coord => {
          const transformed = transform(coord, 'EPSG:3857', 'EPSG:4326')
          return { lng: transformed[0], lat: transformed[1] }
        })
      }

      data.push({
        name,
        type: geometryType,
        coordinates
      })
    })

    return JSON.stringify(data, null, 2)
  }

  /**
   * 导出测量数据为坐标列表（包含测量结果）
   */
  exportMeasureAsCoordinates() {
    if (!this.sources.measure) return null

    const features = this.sources.measure.getFeatures()
    const data = []

    features.forEach((feature, index) => {
      const geometry = feature.getGeometry()
      const geometryType = geometry.getType()
      const name = feature.get('name') || `测量 ${index + 1}`

      let coordinates = []
      let measureResult = null

      if (geometryType === 'LineString') {
        coordinates = geometry.getCoordinates().map(coord => {
          const transformed = transform(coord, 'EPSG:3857', 'EPSG:4326')
          return { lng: transformed[0], lat: transformed[1] }
        })

        // 计算距离
        const length = getLength(geometry)
        measureResult = {
          type: 'distance',
          value: length,
          formatted: length > 1000 ?
            `${(Math.round(length / 1000 * 1000) / 1000)} km` :
            `${(Math.round(length * 100) / 100)} m`
        }
      } else if (geometryType === 'Polygon') {
        // 只导出外环坐标
        const ring = geometry.getCoordinates()[0]
        coordinates = ring.map(coord => {
          const transformed = transform(coord, 'EPSG:3857', 'EPSG:4326')
          return { lng: transformed[0], lat: transformed[1] }
        })

        // 计算面积
        const area = getArea(geometry)
        let formatted = ''
        if (area > 1000000) {
          formatted = `${(Math.round(area / 1000000 * 1000) / 1000)} km²`
        } else if (area > 10000) {
          formatted = `${(Math.round(area / 10000 * 100) / 100)} 公顷`
        } else {
          formatted = `${(Math.round(area * 100) / 100)} m²`
        }

        measureResult = {
          type: 'area',
          value: area,
          formatted
        }
      }

      data.push({
        name,
        type: geometryType,
        coordinates,
        measureResult
      })
    })

    return JSON.stringify(data, null, 2)
  }

  /**
   * 导出所有数据为GeoJSON
   */
  exportAllAsGeoJSON() {
    const drawFeatures = this.sources.draw ? this.sources.draw.getFeatures() : []
    const measureFeatures = this.sources.measure ? this.sources.measure.getFeatures() : []

    if (drawFeatures.length === 0 && measureFeatures.length === 0) return null

    const format = new GeoJSON()
    const allFeatures = [...drawFeatures, ...measureFeatures]

    return format.writeFeatures(allFeatures, {
      featureProjection: 'EPSG:3857',
      dataProjection: 'EPSG:4326'
    })
  }

  /**
   * 导出所有数据为KML
   */
  exportAllAsKML() {
    const drawFeatures = this.sources.draw ? this.sources.draw.getFeatures() : []
    const measureFeatures = this.sources.measure ? this.sources.measure.getFeatures() : []

    if (drawFeatures.length === 0 && measureFeatures.length === 0) return null

    const format = new KML()
    const allFeatures = [...drawFeatures, ...measureFeatures]

    return format.writeFeatures(allFeatures, {
      featureProjection: 'EPSG:3857',
      dataProjection: 'EPSG:4326'
    })
  }

  /**
   * 销毁图层管理器
   */
  destroy() {
    if (!this.map) return

    // 移除图层
    if (this.layers.draw) {
      this.map.removeLayer(this.layers.draw)
    }
    if (this.layers.measure) {
      this.map.removeLayer(this.layers.measure)
    }

    // 清除覆盖物
    this.removeOverlays('draw')
    this.removeOverlays('measure')

    // 重置状态
    this.map = null
    this.layers = { draw: null, measure: null }
    this.sources = { draw: null, measure: null }
    this.overlays = { draw: [], measure: [] }
    this.initialized = false

    console.log('全局图层管理器已销毁')
  }
}

// 创建单例实例
const globalLayerManager = new GlobalLayerManager()

export default globalLayerManager
