# 地图数据导出功能使用说明

## 功能概述

新增的导出功能允许您将在地图上绘制的点、线、面以及测量的距离、面积数据导出为文件。支持多种格式：

- **GeoJSON格式**：标准的地理数据交换格式，可被大多数GIS软件读取
- **KML格式**：Google Earth等软件支持的格式
- **坐标列表格式**：JSON格式的坐标数据，包含详细的测量结果

## 使用方法

### 1. 绘制或测量数据

首先，您需要在地图上绘制一些内容或进行测量：

- 使用**绘制工具**：点击工具栏中的"绘制"按钮，选择绘制点、线、曲线或多边形
- 使用**测量工具**：点击工具栏中的"测量"按钮，选择测量距离或面积

### 2. 导出数据

当您有了绘制内容或测量数据后：

1. 点击页面左上角的**"数据导出"**按钮（位于省份搜索按钮下方）
2. 在弹出的导出菜单中选择您需要的导出选项：

#### 导出选项说明

**绘制内容导出：**
- **绘制内容 (GeoJSON)**：导出所有绘制的点、线、面为GeoJSON格式
- **绘制内容 (KML)**：导出所有绘制的点、线、面为KML格式
- **绘制内容 (坐标)**：导出所有绘制内容的坐标列表

**测量数据导出：**
- **测量数据 (GeoJSON)**：导出所有测量的线和面为GeoJSON格式
- **测量数据 (KML)**：导出所有测量的线和面为KML格式
- **测量数据 (坐标)**：导出测量数据的坐标列表，包含距离和面积结果

**全部数据导出：**
- **全部数据 (GeoJSON)**：导出所有绘制和测量数据为GeoJSON格式
- **全部数据 (KML)**：导出所有绘制和测量数据为KML格式

### 3. 文件下载

点击导出选项后，文件将自动下载到您的默认下载文件夹。文件名格式为：
```
[数据类型]_[时间戳].[扩展名]
```

例如：
- `绘制内容_20241201_164530.geojson`
- `测量数据_20241201_164530.kml`
- `全部数据_20241201_164530.geojson`

## 数据格式说明

### GeoJSON格式
标准的地理数据格式，包含几何信息和属性数据。可以被QGIS、ArcGIS等GIS软件直接读取。

### KML格式
Google Earth支持的格式，可以直接在Google Earth中查看您的绘制内容。

### 坐标列表格式
JSON格式的坐标数据，结构如下：

```json
[
  {
    "name": "点 1",
    "type": "Point",
    "coordinates": [
      { "lng": 114.3054, "lat": 30.5928 }
    ]
  },
  {
    "name": "测量 1",
    "type": "LineString",
    "coordinates": [
      { "lng": 114.3054, "lat": 30.5928 },
      { "lng": 114.3154, "lat": 30.6028 }
    ],
    "measureResult": {
      "type": "distance",
      "value": 1234.56,
      "formatted": "1.23 km"
    }
  }
]
```

## 注意事项

1. **数据持久性**：绘制和测量的数据在切换功能时会保持显示，不会丢失
2. **坐标系统**：导出的坐标使用WGS84坐标系统（EPSG:4326）
3. **文件大小**：大量的绘制数据可能产生较大的文件
4. **浏览器兼容性**：现代浏览器都支持文件下载功能

## 常见问题

**Q: 为什么导出菜单显示"暂无可导出的数据"？**
A: 这表示您还没有在地图上绘制任何内容或进行任何测量。请先使用绘制工具或测量工具创建一些数据。

**Q: 导出的文件在哪里？**
A: 文件会下载到您浏览器设置的默认下载文件夹中。

**Q: 可以导出特定的图形吗？**
A: 目前支持按类型导出（绘制内容、测量数据或全部数据），暂不支持选择特定图形导出。

**Q: 导出的坐标精度如何？**
A: 坐标精度保持原始精度，测量结果会根据数值大小自动选择合适的单位显示。
