<template>
  <div class="topleft-export-btn" @click="toggleExportTools" ref="exportBtn">
    <span class="btn-icon">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z M12,19L8,15H10.5V12H13.5V15H16L12,19Z"></path>
      </svg>
    </span>
    <span class="btn-text">数据导出</span>
    <span class="btn-arrow">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="currentColor">
        <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
      </svg>
    </span>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const exportBtn = ref(null)
const emit = defineEmits(['toggle-export-tools'])

function toggleExportTools() {
  // 获取按钮位置
  if (exportBtn.value) {
    const rect = exportBtn.value.getBoundingClientRect()
    emit('toggle-export-tools', { rect })
  } else {
    emit('toggle-export-tools', {})
  }
}
</script>

<style scoped>
.topleft-export-btn {
  position: absolute;
  top: 92px; /* 省份搜索按钮下方，省份搜索按钮top(32px) + 高度(约44px) + 间距(16px) */
  left: 90px; 
  z-index: 1010;
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 4px;
  padding: 10px 16px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;
  color: #1aa2c4;
  font-weight: 500;
  border: 1px solid #f6ffed;
  width: 180px;
  justify-content: flex-start;
}

.topleft-export-btn::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  background-color: #409EFF;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  transition: height 0.3s ease;
  height: 0;
}

.topleft-export-btn:hover {
  background-color: #f6ffed;
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.15);
}

.topleft-export-btn:active {
  background-color: #f6ffed;
  box-shadow: 0 2px 6px rgba(82, 196, 26, 0.1);
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  color: #409EFF;
  background-color: rgba(82, 196, 26, 0.1);
  border-radius: 50%;
  padding: 6px;
  width: 24px;
  height: 24px;
}

.btn-text {
  white-space: nowrap;
  letter-spacing: 0.5px;
  font-weight: 500;
  flex-grow: 1;
}

.btn-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #409EFF;
  opacity: 0.7;
  transition: all 0.3s;
  margin-left: 5px;
}

.topleft-export-btn:hover .btn-arrow {
  opacity: 1;
  transform: translateY(2px);
}

/* 添加动画效果 */
.topleft-export-btn {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 添加动画 */
.btn-icon svg {
  transition: transform 0.3s ease;
}

.topleft-export-btn:hover .btn-icon svg {
  transform: scale(1.1);
}

.topleft-export-btn:hover::before {
  height: 100%;
}
</style>
